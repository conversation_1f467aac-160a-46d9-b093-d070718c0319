@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import 'video.js/dist/video-js.css';
@import './styles/esl-player.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles */
@layer base {
  html, body {
    height: 100%;
    overflow: hidden;
  }

  body {
    font-family: 'Inter', system-ui, sans-serif;
    @apply bg-gray-50 text-gray-900;
  }

  #root {
    height: 100%;
    overflow: hidden;
  }

  * {
    @apply border-gray-200;
  }
}

/* Custom component styles */
@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .input-field {
    @apply block w-full rounded-lg border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500;
  }

  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }

  .word-highlight {
    @apply bg-yellow-200 text-yellow-900 px-1 rounded;
  }

  .word-highlight-active {
    @apply bg-yellow-400 text-yellow-900 px-1 rounded font-semibold;
  }

  .current-word-highlight {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%) !important;
    color: #1f2937 !important;
    padding: 3px 6px !important;
    border-radius: 6px !important;
    font-weight: 700 !important;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2) !important;
    animation: pulse-highlight 0.4s ease-in-out !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
  }
}

/* Video player container */
.video-player-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  background: #000;
  border-radius: 0.5rem;
  overflow: hidden;
}

.video-player-container .video-js {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 0.5rem;
}

/* Video.js theme customization */
.video-js {
  font-family: 'Inter', system-ui, sans-serif;
}

/* Disable ALL native browser video controls */
.esl-video-player video,
.esl-video-player .video-js video {
  -webkit-media-controls: none !important;
  -moz-media-controls: none !important;
  media-controls: none !important;
}

.esl-video-player video::-webkit-media-controls,
.esl-video-player video::-webkit-media-controls-enclosure,
.esl-video-player video::-webkit-media-controls-panel,
.esl-video-player video::-webkit-media-controls-play-button,
.esl-video-player video::-webkit-media-controls-start-playback-button,
.esl-video-player video::-webkit-media-controls-timeline,
.esl-video-player video::-webkit-media-controls-current-time-display,
.esl-video-player video::-webkit-media-controls-time-remaining-display,
.esl-video-player video::-webkit-media-controls-mute-button,
.esl-video-player video::-webkit-media-controls-volume-slider,
.esl-video-player video::-webkit-media-controls-fullscreen-button {
  display: none !important;
  -webkit-appearance: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

.esl-video-player video::-moz-media-controls {
  display: none !important;
}

.video-js .vjs-big-play-button {
  background-color: rgba(37, 99, 235, 0.9);
  border: none;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  line-height: 80px;
  font-size: 2rem;
  top: 50%;
  left: 50%;
  margin-top: -40px;
  margin-left: -40px;
  transition: all 0.3s ease;
}

.video-js .vjs-big-play-button:hover {
  background-color: rgba(37, 99, 235, 1);
  transform: scale(1.1);
}

.video-js .vjs-control-bar {
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.8) 100%);
  height: 4rem;
}

/* Progress Bar Styling - Complete Implementation */
.video-js .vjs-progress-control {
  height: 8px !important;
  position: relative;
  margin: 0 8px;
}

.video-js .vjs-progress-holder {
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 4px !important;
  height: 8px !important;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.video-js .vjs-progress-control:hover .vjs-progress-holder {
  background: rgba(255, 255, 255, 0.3) !important;
  transform: scaleY(1.2);
}

.video-js .vjs-load-progress {
  background: rgba(255, 255, 255, 0.4) !important;
  border-radius: 4px !important;
}

.video-js .vjs-play-progress {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8) !important;
  border-radius: 4px !important;
  position: relative;
  transition: all 0.2s ease;
}

.video-js .vjs-play-progress::before {
  content: '';
  position: absolute;
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  background: #ffffff;
  border: 2px solid #3b82f6;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-js .vjs-progress-control:hover .vjs-play-progress::before {
  opacity: 1;
}

.video-js .vjs-mouse-display {
  background: rgba(255, 255, 255, 0.8) !important;
  border-radius: 4px !important;
}

/* Volume Control Styling */
.video-js .vjs-volume-control {
  width: 5em;
}

.video-js .vjs-volume-bar {
  background: rgba(255, 255, 255, 0.2) !important;
  border-radius: 2px !important;
  height: 4px !important;
}

.video-js .vjs-volume-level {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8) !important;
  border-radius: 2px !important;
  height: 4px !important;
}

/* Control Bar Button Styling */
.video-js .vjs-control-bar .vjs-button {
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  border-radius: 4px;
}

.video-js .vjs-control-bar .vjs-button:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
}

/* Time Display Styling */
.video-js .vjs-time-control {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 500;
}

/* Fullscreen Button */
.video-js .vjs-fullscreen-control {
  order: 10;
}

/* Hide ALL Video.js controls completely in ESL player */
.esl-video-player .video-js .vjs-control-bar,
.esl-video-player .video-js .vjs-big-play-button,
.esl-video-player .video-js .vjs-loading-spinner,
.esl-video-player .video-js .vjs-progress-control,
.esl-video-player .video-js .vjs-play-control,
.esl-video-player .video-js .vjs-volume-panel,
.esl-video-player .video-js .vjs-current-time,
.esl-video-player .video-js .vjs-time-divider,
.esl-video-player .video-js .vjs-duration,
.esl-video-player .video-js .vjs-fullscreen-control,
.esl-video-player .video-js .vjs-remaining-time,
.esl-video-player .video-js .vjs-playback-rate,
.esl-video-player .video-js .vjs-menu-button {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
  pointer-events: none !important;
  height: 0 !important;
  width: 0 !important;
  overflow: hidden !important;
}

/* Custom subtitle styling */
.vjs-text-track-display {
  pointer-events: none;
}

.vjs-text-track-cue {
  background-color: rgba(0, 0, 0, 0.8) !important;
  color: white !important;
  font-size: 1.2em !important;
  line-height: 1.4 !important;
  padding: 0.2em 0.5em !important;
  border-radius: 0.25rem !important;
}

/* ESL controls styling */
.esl-controls {
  @apply flex flex-wrap gap-2 mt-4 p-4 bg-gray-50 rounded-lg;
}

.esl-controls button {
  @apply px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200;
}

.esl-controls button:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* Transcript panel styles moved to esl-player.css */

/* Upload progress */
.upload-progress {
  @apply w-full bg-gray-200 rounded-full h-2;
}

.upload-progress-bar {
  @apply bg-primary-600 h-2 rounded-full transition-all duration-300;
}

/* Word highlighting animations */
@keyframes pulse-highlight {
  0% {
    transform: scale(1);
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
  }
  50% {
    transform: scale(1.08);
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
  100% {
    transform: scale(1);
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
  }
}
