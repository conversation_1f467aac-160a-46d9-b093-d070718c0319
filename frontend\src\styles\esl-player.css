/* ESL Video Player Styles */

.esl-video-player {
  /* Container styles */
  height: 100%;
  display: flex;
  flex-direction: column;
}

.esl-video-player .video-js {
  /* Ensure Video.js player takes full width and height */
  width: 100% !important;
  height: 100% !important;
  object-fit: contain;
}

/* Hide all Video.js controls completely */
.esl-video-player .video-js .vjs-control-bar,
.esl-video-player .video-js .vjs-big-play-button,
.esl-video-player .video-js .vjs-loading-spinner,
.esl-video-player .video-js .vjs-menu-button,
.esl-video-player .video-js .vjs-play-control,
.esl-video-player .video-js .vjs-volume-panel,
.esl-video-player .video-js .vjs-current-time,
.esl-video-player .video-js .vjs-time-divider,
.esl-video-player .video-js .vjs-duration,
.esl-video-player .video-js .vjs-progress-control,
.esl-video-player .video-js .vjs-fullscreen-control,
.esl-video-player .video-js .vjs-remaining-time,
.esl-video-player .video-js .vjs-playback-rate,
.esl-video-player .video-js .vjs-chapters-button,
.esl-video-player .video-js .vjs-descriptions-button,
.esl-video-player .video-js .vjs-captions-button,
.esl-video-player .video-js .vjs-subtitles-button,
.esl-video-player .video-js .vjs-audio-button,
.esl-video-player .video-js .vjs-picture-in-picture-control {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* Hide native browser video controls */
.esl-video-player video::-webkit-media-controls,
.esl-video-player video::-webkit-media-controls-enclosure,
.esl-video-player video::-webkit-media-controls-panel,
.esl-video-player video::-webkit-media-controls-play-button,
.esl-video-player video::-webkit-media-controls-start-playback-button {
  display: none !important;
  -webkit-appearance: none !important;
}

.esl-video-player video::-moz-media-controls {
  display: none !important;
}

.esl-video-player video {
  /* Disable native controls completely */
  -webkit-media-controls: none !important;
  -moz-media-controls: none !important;
  media-controls: none !important;
}

/* Video player container with proper aspect ratio handling */
.video-player-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 16px;
  overflow: hidden;
}

.video-player-container .video-js .vjs-tech {
  object-fit: contain;
  width: 100%;
  height: 100%;
}

/* Focus styles for accessibility */
.esl-video-player button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Fixed layout container */
.player-layout-container {
  display: flex;
  height: 100%;
  gap: 1rem;
}

/* Transcript panel scrolling */
.transcript-panel {
  overflow-y: auto;
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
  height: 100%;
}

.transcript-panel::-webkit-scrollbar {
  width: 6px;
}

.transcript-panel::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 3px;
}

.transcript-panel::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.transcript-panel::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Smooth scrolling for active segment */
.transcript-panel .group {
  scroll-margin-top: 1rem;
}

/* Focus mode styling */
.focus-mode .transcript-panel {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* Enhanced segment hover effects - Removed unnecessary animation */
.transcript-panel .group:hover {
  /* Removed translateX animation for cleaner UX */
}

/* Active segment pulse animation */
@keyframes pulse-blue {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse-blue 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Transcript Panel Styles */
.transcript-panel {
  max-height: 400px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.transcript-panel::-webkit-scrollbar {
  width: 6px;
}

.transcript-panel::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 3px;
}

.transcript-panel::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.transcript-panel::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

.transcript-segment {
  margin-bottom: 4px;
  transition: all 0.2s ease;
}

/* Updated styles for the new structure with padding and hover on the inner div */
.transcript-segment > div {
  transition: all 0.2s ease;
}

.transcript-segment > div:hover {
  background-color: #f8fafc;
}

/* Active segment styling */
.transcript-segment > div.bg-blue-50 {
  background-color: #eff6ff !important;
  border-left: 4px solid #3b82f6;
}

/* Ensure proper spacing and visual hierarchy */
.transcript-segment .text-xs {
  min-width: 60px;
}

.word-highlight {
  background-color: #fef5e7;
  padding: 1px 2px;
  border-radius: 2px;
  transition: background-color 0.2s ease;
}

/* ESL Controls Styling */
.esl-controls {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  padding: 20px;
}

.esl-controls button {
  transition: all 0.2s ease;
}

.esl-controls button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Mode buttons */
.mode-button {
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 600;
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.mode-button.active {
  border-color: white;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}

/* Segment navigation */
.segment-nav {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.segment-text {
  background: rgba(255, 255, 255, 0.9);
  color: #2d3748;
  padding: 16px;
  border-radius: 8px;
  line-height: 1.6;
  font-size: 16px;
}

/* Speed controls */
.speed-control select {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  padding: 6px 12px;
  color: #2d3748;
}

/* Loading states */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .esl-video-player {
    padding: 16px;
  }
  
  .esl-controls {
    padding: 16px;
  }
  
  .mode-button {
    padding: 8px 16px;
    font-size: 14px;
  }
  
  .segment-nav {
    flex-direction: column;
    gap: 8px;
  }
  
  .transcript-panel {
    max-height: 300px;
  }
}

/* Search highlighting */
mark {
  background-color: #fef08a;
  padding: 1px 2px;
  border-radius: 2px;
  color: inherit;
}

/* Focus states for accessibility */
.transcript-segment:focus,
.mode-button:focus,
button:focus {
  outline: 2px solid #3182ce;
  outline-offset: 2px;
}

/* Animation for segment transitions */
.segment-transition {
  animation: segmentHighlight 0.5s ease-in-out;
}

@keyframes segmentHighlight {
  0% { background-color: #fef5e7; }
  50% { background-color: #fed7aa; }
  100% { background-color: #ebf8ff; }
}

/* Word-level highlighting styles */
.word-highlighter {
  position: relative;
  z-index: 1000;
  pointer-events: none;
}

.current-word-highlight {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%) !important;
  color: #1f2937 !important;
  padding: 3px 6px !important;
  border-radius: 6px !important;
  font-weight: 700 !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2) !important;
  animation: wordPulse 0.4s ease-in-out !important;
  transition: all 0.2s ease !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

@keyframes wordPulse {
  0% {
    transform: scale(1);
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
  }
  50% {
    transform: scale(1.08);
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
  100% {
    transform: scale(1);
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
  }
}

/* Word highlighter overlay */
.word-highlighter .bg-white\/95 {
  background-color: rgba(255, 255, 255, 0.95);
}

/* Alternative highlighting styles for different contexts */
.word-highlight-subtle {
  background-color: #fef3c7;
  padding: 1px 3px;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.word-highlight-emphasis {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 2px 6px;
  border-radius: 6px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  animation: emphasisPulse 0.4s ease-in-out;
}

@keyframes emphasisPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.08); }
  100% { transform: scale(1); }
}

/* Control Panel Styles */
.control-button-panel {
  @apply bg-slate-700 hover:bg-slate-600 text-white p-2 rounded-lg transition-colors duration-200 flex items-center justify-center;
  min-width: 40px;
  height: 40px;
}

.control-button-panel.play-button-panel {
  @apply bg-blue-600 hover:bg-blue-700;
  min-width: 48px;
  height: 48px;
}

.speed-dropdown-panel {
  @apply bg-slate-700 text-white border border-slate-600 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.custom-progress-bar-control {
  @apply w-full h-2 bg-slate-600 rounded-full cursor-pointer relative;
}

.progress-track-control {
  @apply w-full h-full bg-slate-600 rounded-full relative overflow-hidden;
}

.buffered-progress-control {
  @apply absolute top-0 left-0 h-full bg-slate-500 rounded-full;
}

.play-progress-control {
  @apply absolute top-0 left-0 h-full bg-blue-500 rounded-full;
}

.progress-handle-control {
  @apply absolute top-1/2 right-0 w-4 h-4 bg-white rounded-full shadow-lg transform -translate-y-1/2 translate-x-1/2 cursor-pointer;
  transition: all 0.2s ease;
}

.progress-handle-control:hover,
.progress-handle-control.dragging {
  @apply w-5 h-5 bg-blue-200;
}

/* Mode Button Styles */
.mode-button {
  @apply px-6 py-2 rounded-lg font-semibold transition-all duration-200 border-2;
}

.mode-button:not(.active) {
  @apply bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200;
}

.mode-button.active {
  @apply bg-blue-600 text-white border-blue-600 shadow-lg;
}
